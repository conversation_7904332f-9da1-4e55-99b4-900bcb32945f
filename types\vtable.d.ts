declare module "@visactor/vue-vtable" {
  import { DefineComponent } from "vue";

  export interface ListColumnSlots {
    customLayout?: (props: { width: number; height: number; record: any; row?: any }) => any;
    edit?: (props: { record: any; row?: any }) => any;
    header?: (props: { column: any }) => any;
  }

  export interface VTableInstance {
    off: (event: string) => void;
    renderWithRecreateCells: () => void;
    updateOption: (option: any) => void;
    [key: string]: any;
  }

  export interface ListTableRef {
    vTableInstance: VTableInstance;
  }

  export const ListColumn: DefineComponent<any, any, any, any, any, any, any, any, false, any, ListColumnSlots>;

  export const ListTable: DefineComponent<
    any,
    any,
    any,
    any,
    any,
    any,
    any,
    any,
    false,
    any,
    any,
    {
      vTableInstance: VTableInstance;
    }
  >;

  export const Text: DefineComponent<any>;
  export const Group: DefineComponent<any>;
  export const Tag: DefineComponent<any>;
  export const VTable: any;
}

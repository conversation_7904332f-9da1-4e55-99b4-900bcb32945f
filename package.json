{"name": "cxist.ai.cablequotation.platform.frontend", "version": "3.0.0", "private": true, "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f src/assets/svg -o src/assets/svg -r", "cloc": "NODE_OPTIONS=--max-old-space-size=4096 cloc . --exclude-dir=node_modules --exclude-lang=YAML", "clean:cache": "rm -rf node_modules && rm -rf .eslintcache && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,css,scss,postcss,less}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "lintcheck": "pnpm  lint && pnpm typecheck", "prepare": "husky install", "preinstall": "npx only-allow pnpm", "plop": "plop"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@pureadmin/descriptions": "^1.1.0", "@pureadmin/table": "^2.0.0", "@pureadmin/utils": "^1.8.5", "@rmp135/vue-splitter": "^2.0.2", "@rollup/plugin-inject": "5.0.5", "@types/qrcode": "^1.5.5", "@visactor/vue-vtable": "^1.19.3", "@vue-office/excel": "^1.7.14", "@vueuse/core": "^9.13.0", "@vueuse/motion": "2.0.0-beta.12", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "axios": "1.2.2", "css-has-pseudo": "^5.0.2", "dayjs": "^1.11.7", "echarts": "^5.4.1", "element-plus": "2.3.8", "element-resize-detector": "^1.2.4", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "jquery": "2.2.4", "js-cookie": "^3.0.1", "luckyexcel": "1.0.1", "luckysheet": "^2.1.13", "markdown-it": "^14.1.0", "marked": "^15.0.11", "mitt": "^3.0.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.0.32", "print-js": "^1.6.0", "qrcode": "^1.5.4", "qs": "^6.11.0", "responsive-storage": "^2.2.0", "socket.io-client": "^4.7.1", "vue": "^3.2.47", "vue-echarts": "^6.6.8", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue-types": "^5.0.2", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.18", "xgplayer-hls": "^3.0.18", "xlsx": "^0.17.5"}, "devDependencies": {"@commitlint/cli": "13.1.0", "@commitlint/config-conventional": "13.1.0", "@iconify-icons/ep": "^1.2.10", "@iconify-icons/ri": "^1.2.4", "@iconify/vue": "^4.1.0", "@intlify/unplugin-vue-i18n": "^0.8.2", "@pureadmin/theme": "^3.0.0", "@types/element-resize-detector": "1.1.3", "@types/js-cookie": "^3.0.1", "@types/mockjs": "^1.0.7", "@types/node": "^18.11.9", "@types/nprogress": "0.2.0", "@types/qs": "^6.9.7", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.2", "autoprefixer": "^10.4.13", "cloc": "^2.11.0", "cssnano": "^5.1.14", "eslint": "^8.8.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^9.9.0", "husky": "^7.0.4", "lint-staged": "11.1.2", "picocolors": "^1.0.0", "plop": "^4.0.1", "postcss": "^8.4.21", "postcss-html": "^1.5.0", "postcss-import": "^15.1.0", "postcss-pxtorem": "^6.0.0", "postcss-scss": "^4.0.6", "prettier": "^2.5.1", "pretty-quick": "3.1.1", "rimraf": "3.0.2", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.57.1", "sass-loader": "^13.2.0", "stylelint": "^14.3.0", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recommended": "^9.0.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "svgo": "^3.0.2", "tailwindcss": "^3.4.1", "terser": "^5.16.1", "typescript": "^4.9.5", "unplugin-vue-define-options": "^1.0.0", "vite": "^4.1.5", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^2.9.8", "vite-plugin-remove-console": "^2.1.0", "vite-svg-loader": "^4.0.0", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^1.2.0"}, "resolutions": {"@intlify/message-compiler": "11.1.2", "@intlify/shared": "11.1.2"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup", "webpack"]}}, "repository": "**************:pure-admin/pure-admin-thin.git", "author": "xiaoxian521", "license": "MIT", "volta": {"node": "18.20.8", "pnpm": "8.15.9"}}
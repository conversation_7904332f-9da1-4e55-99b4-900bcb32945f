<template>
  <div class="overflow-hidden w-full flex flex-col flex-1">
    <div class="py-3 bg-bg_color">
      <QuotationHeader
        :quotationInquiryExport="state.quotationInquiryExport"
        class="w-full"
        :taskId="state.taskId"
        :fileNames="state.uploadRawFiles?.map(item => item.name)"
        :uploadPercentage="state.uploadPercentage"
        :quotationInquiryProgress="state.quotationInquiryProgress"
        @onStartParsing="handleStartParsing()"
        @onUploadFile="onSelectUploadFile($event)"
        @onStopParsing="handleStopParsing()"
        @onReParseing="handleonReParseing()"
      />
    </div>
    <!-- <div class="risk-tip flex items-center justify-between">
      <div class="flex items-center">
        <div class="tip">当前询价单存在重复询价风险，点击</div>
        <div class="link">查看详情</div>
      </div>
      <div class="close">
        <el-icon><Close /></el-icon>
      </div>
    </div> -->
    <div class="bg-bg_color p-5 pb-1 my-5 mx-6 flex flex-col flex-1 overflow-hidden relative">
      <div class="flex justify-between">
        <div class="flex flex-1">
          <el-row :gutter="20">
            <el-col :span="17">
              <ElForm
                :inline="true"
                :model="state.quotationInquiryExport"
                class="p-0 party"
                label-width="70"
                label-position="left"
              >
                <el-row :gutter="20">
                  <el-col :span="8">
                    <ElFormItem label="询价单位" class="inquiry-organization !w-full" prop="inquiryCompanyId">
                      <el-select
                        clearable
                        filterable
                        class="!w-full"
                        placeholder="请选择询价单位"
                        v-model="state.quotationInquiryExport.inquiryCompanyId"
                      >
                        <el-option
                          v-for="item in state.inquiryOrganizationList || []"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        />
                      </el-select>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="联系人" class="contract w-full" prop="contactPerson">
                      <ElInput
                        clearable
                        placeholder="请输入联系人"
                        v-model="state.quotationInquiryExport.contactPerson"
                      />
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="联系方式" prop="contactInfo" class="w-full">
                      <ElInput
                        clearable
                        placeholder="请输入联系方式"
                        v-model="state.quotationInquiryExport.contactInfo"
                      />
                    </ElFormItem>
                  </el-col>
                  <!-- <el-col :span="5">
                    <ElFormItem label="原始规格型号列" prop="subject" class="show-original-row">
                      <el-switch v-model="state.toggleOriginalRow" @change="onToggleOriginalRow()" />
                    </ElFormItem>
                  </el-col> -->

                  <el-col :span="8">
                    <ElFormItem label="铜价" prop="copperPrice" class="copper-price w-full">
                      <ElInput type="number" v-model="state.quotationInquiryExport.copperPrice" placeholder="请输入">
                        <template #append>元/吨</template>
                      </ElInput>
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="主题" prop="subject" class="w-full">
                      <ElInput clearable placeholder="请输入主题" v-model="state.quotationInquiryExport.subject" />
                    </ElFormItem>
                  </el-col>
                  <el-col :span="8">
                    <ElFormItem label="参考" class="rerfence w-full" prop="reference">
                      <ElInput clearable placeholder="请输入参考" v-model="state.quotationInquiryExport.reference" />
                    </ElFormItem>
                  </el-col>
                </el-row>
              </ElForm>
            </el-col>
            <el-col :span="6">
              <ElForm :inline="true" :model="state.quotationInquiryExport" class="p-0">
                <ElFormItem label="备注" class="w-full pr-6 remark" prop="remark">
                  <ElInput
                    :rows="3"
                    resize="none"
                    class="!w-full"
                    clearable
                    placeholder="请输入"
                    type="textarea"
                    v-model="state.quotationInquiryExport.remark"
                  />
                </ElFormItem>
              </ElForm>
            </el-col>
          </el-row>
        </div>
        <div class="action">
          <div>
            <ElButton
              :icon="Discount"
              :disabled="disabledEditDiscount"
              v-auth="PermissionKey.quotation.quotationManagementEdit"
              @click="onBatchQuotationDiscount()"
              >批量折扣</ElButton
            >
            <ElButton
              :icon="CopyDocument"
              v-auth="PermissionKey.quotation.quotationManagementCreate"
              @click="onPasteSpecification()"
              >粘贴型号规格</ElButton
            >
            <ElButton
              :icon="Plus"
              type="primary"
              :disabled="disabledEditDiscount"
              v-auth="PermissionKey.quotation.quotationManagementCreate"
              @click="onAddModelNameSpecificationDialogVisible()"
              >添加型号规格</ElButton
            >
          </div>
          <div
            class="absolute right-5 mt-2 w-[40px] text-center cursor-pointer"
            @click="onToggleDispalyColumnVisible()"
          >
            <!-- <el--model="state.toggleOriginalRow" label="原始信息" @change="onToggleOriginalRow()" />
            <el-checkbox v-model="state.toggleDiscount" label="折扣" @change="onToggleDiscountRow()" /> -->
            <el-tooltip class="box-item" effect="dark" content="设置列表显示字段" placement="top">
              <el-icon><Setting /></el-icon>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="flex-1 overflow-hidden table-container" v-loading="applyAiRecommendModelLoading">
        <ListTable :options="tableOption" :records="state.list" ref="listTableRef">
          <ListColumn
            field="checkbox"
            width="50"
            cell-type="checkbox"
            header-type="checkbox"
            :disableColumnResize="true"
            :headerStyle="{ checkboxStyle: { checkedFill: '#00b678', checkedStroke: '#00b678' } }"
            :style="{ checkboxStyle: { checkedFill: '#00b678', checkedStroke: '#00b678' } }"
          />
          <template v-for="column in columnsRef">
            <template v-if="column.dataKey === 'rowIndex'">
              <ListColumn
                :key="column.dataKey"
                field="rowIndex"
                title="序号"
                cell-type="text"
                :headerStyle="headerStyle"
                :width="column.width"
                minWidth="60"
                max-width="60"
                :disableColumnResize="true"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
              >
                <template #customLayout="{ width, height, row }">
                  <Group :width="width" :height="height" display="flex" align-items="center" justify-content="center">
                    <Text :text="row" :fontSize="13" />
                  </Group>
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'excelRowIndex'">
              <ListColumn
                field="excelRowIndex"
                :key="column.dataKey"
                title="原始位置"
                :headerStyle="headerStyle"
                :width="column.width"
                :disableColumnResize="true"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
              >
                <template #customLayout="{ width, height, record }">
                  <Group :width="width" :height="height" display="flex" align-items="center" justify-content="center">
                    <Text :fontSize="13" :text="record.excelSheet + '-' + record.excelRowIndex" />
                  </Group>
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'columnRawContent'">
              <ListColumn
                :key="column.dataKey"
                field="columnRawContent"
                title="原始数据"
                :width="column.width || 220"
                minWidth="110"
                :autoWrapText="true"
                :headerIcon="state.searchKeyword?.columnRawContent ? filterIconActivate : filterIcon"
                :headerStyle="headerStyle"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
              />
            </template>
            <template v-else-if="column.dataKey === 'modelName'">
              <ListColumn
                :key="column.dataKey"
                field="modelName"
                title="型号"
                :minWidth="100"
                :width="column.width || state.modelNameColumnWidth"
                :headerStyle="headerStyle"
                :headerIcon="
                  state.searchKeyword?.modelName ||
                  (state.searchKeyword?.voltageLevel && voltageLevelColumn.voltageLevelMergeModelName) ||
                  (state.searchKeyword?.specification && specificationColumn.specificationMergeModelName)
                    ? filterIconModelNameActivate
                    : filterIconModelName
                "
                editor="dynamic-render-editor"
                :hide="column.hidden"
              >
                <template #customLayout="{ width, height, record }">
                  <Group :width="width" :height="height" display="flex" flexWrap="nowrap" alignItems="center">
                    <Text text="  " />
                    <Text :text="getModelName(record)" :fontSize="13" fontFamily="sans-serif" fill="black" />
                    <Text text="  " />
                  </Group>
                </template>
                <template #edit="{ record }">
                  <ModelNameCell
                    :row-data="record"
                    :specification-column="specificationColumn"
                    :voltage-level-column="voltageLevelColumn"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'modelName,voltageLevel,specification')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'voltageLevel'">
              <ListColumn
                :key="column.dataKey"
                field="voltageLevel"
                title="电压"
                :width="column.width || 100"
                minWidth="100"
                editor="dynamic-render-editor"
                :headerIcon="state.searchKeyword?.voltageLevel ? filterIconActivate : filterIcon"
                :headerStyle="headerStyle"
                :style="{ cursor: 'pointer' }"
                :hide="column.hidden"
                class="voltageLevel"
              >
                <template #edit="{ record }">
                  <EditStringCell
                    :data="record"
                    field="voltageLevel"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'voltageLevel')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'specification'">
              <ListColumn
                :key="column.dataKey"
                field="specification"
                title="规格"
                :width="column.width || 100"
                minWidth="100"
                editor="dynamic-render-editor"
                :headerIcon="state.searchKeyword?.specification ? filterIconActivate : filterIcon"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditStringCell
                    :data="record"
                    field="specification"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'specification')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'aiRecommendModel'">
              <ListColumn
                :key="column.dataKey"
                field="aiRecommendModel"
                title="AI推荐型号"
                :headerIcon="state.searchKeyword?.aiRecommendModel ? filterIconActivate : filterIcon"
                minWidth="120"
                :width="column.width || 260"
                :headerStyle="headerStyle"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
              >
                <template #customLayout="{ record, height, width }">
                  <Group
                    :height="height"
                    :width="width - 10"
                    display="flex"
                    flexWrap="nowrap"
                    alignItems="center"
                    justifyContent="space-between"
                  >
                    <Group display="flex">
                      <Text text="  " />
                      <Text
                        :text="
                          record.secondParseModelName +
                          '-' +
                          record.secondParseVoltageLevel +
                          '-' +
                          record.secondParseSpecification
                        "
                        :fontSize="13"
                        fontFamily="sans-serif"
                        fill="black"
                      />
                    </Group>
                    <Tag
                      @click="onApplyAiRecommendModel(record)"
                      v-if="record.secondParseStatus === SecondParseStatusEnum.PARSED"
                      text="应用"
                      :textStyle="{
                        fontSize: 11,
                        fontFamily: 'sans-serif',
                        fill: '#ffffff',
                        cursor: 'pointer'
                      }"
                      :panel="{ visible: true, fill: '#00b678', cornerRadius: 5 }"
                      :padding="[4, 4, 6, 6]"
                    />
                  </Group>
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'quantity'">
              <ListColumn
                :key="column.dataKey"
                field="quantity"
                title="数量"
                minWidth="80"
                :width="column.width || 110"
                editor="dynamic-render-editor"
                :headerStyle="headerStyle"
                :hide="column.hidden"
                :fieldFormat="(record: Record<string,any>) => formatThousands(record.quantity)"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="quantity"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'quantity')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'unit'">
              <ListColumn
                :key="column.dataKey"
                field="unit"
                title="单位"
                minWidth="80"
                :width="column.width || 90"
                editor="dynamic-render-editor"
                :headerIcon="state.searchKeyword?.unit ? filterIconActivate : filterIcon"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditStringCell
                    :data="record"
                    field="unit"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'unit')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'shortMeterDiscount'">
              <ListColumn
                :key="column.dataKey"
                field="shortMeterDiscount"
                title="短米折扣 (%)"
                minWidth="120"
                :width="column.width || 120"
                editor="dynamic-render-editor"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="shortMeterDiscount"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'shortMeterDiscount')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'discount1'">
              <ListColumn
                :key="column.dataKey"
                field="discount1"
                title="折扣1 (%)"
                editor="dynamic-render-editor"
                :width="column.width || 95"
                minWidth="95"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="discount1"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'discount1')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'discount2'">
              <ListColumn
                :key="column.dataKey"
                field="discount2"
                title="折扣2 (%)"
                editor="dynamic-render-editor"
                :width="column.width || 95"
                minWidth="95"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="discount2"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'discount2')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'discount3'">
              <ListColumn
                :key="column.dataKey"
                field="discount3"
                title="折扣3 (%)"
                editor="dynamic-render-editor"
                :width="column.width || 95"
                minWidth="95"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="discount3"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'discount3')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'discount4'">
              <ListColumn
                :key="column.dataKey"
                field="discount4"
                title="折扣4 (%)"
                editor="dynamic-render-editor"
                :width="column.width || 95"
                minWidth="95"
                :headerStyle="headerStyle"
                :hide="column.hidden"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="discount4"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'discount4')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'discountBeforeUnitPrice'">
              <ListColumn
                :key="column.dataKey"
                field="discountBeforeUnitPrice"
                title="折扣前单价"
                editor="dynamic-render-editor"
                :width="column.width || 110"
                minWidth="110"
                :headerStyle="headerStyle"
                :hide="column.hidden"
                :fieldFormat="(record: Record<string,any>) => formatThousands(record.discountBeforeUnitPrice)"
              >
                <template #edit="{ record }">
                  <EditNumberCell
                    :data="record"
                    field="discountBeforeUnitPrice"
                    :disabled="disabledEditDiscount"
                    :handleOnEditSuccess="() => handleOnEditSuccess(record, 'discountBeforeUnitPrice')"
                  />
                </template>
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'taxUnitPrice'">
              <ListColumn
                :key="column.dataKey"
                field="taxUnitPrice"
                title="含税单价"
                :headerStyle="headerStyle"
                minWidth="95"
                :width="column.width || 95"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
                :fieldFormat="(record: Record<string,any>) => formatThousands(record.taxUnitPrice)"
              />
            </template>
            <template v-else-if="column.dataKey === 'taxTotalAmount'">
              <ListColumn
                :key="column.dataKey"
                field="taxTotalAmount"
                title="含税合计"
                :headerStyle="headerStyle"
                minWidth="95"
                :width="column.width || 95"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
                :fieldFormat="(record: Record<string,any>) => formatThousands(record.taxTotalAmount)"
              />
            </template>
            <template v-else-if="column.dataKey === 'priceMatchRecord'">
              <ListColumn
                :key="column.dataKey"
                field="priceMatchRecord"
                title="匹配说明"
                :headerStyle="headerStyle"
                minWidth="100"
                :width="column.width || 100"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
                :style="{
                      color(args: Record<string, any>) {
                      return getPriceMatchRecordColorByIsMatchPrice(args);
                  }
                }"
              >
                <!-- <template #customLayout="{ record, height, width }">
                  <Group :height="height" :width="width" display="flex" flexWrap="nowrap" alignItems="center">
                    <Text text=" " />
                    <Text
                      :text="record.priceMatchRecord"
                      :fontSize="13"
                      :fill="!record.isMatchPrice ? '#e6a23c' : 'black'"
                    />
                    <Text text=" " />
                  </Group>
                </template> -->
              </ListColumn>
            </template>
            <template v-else-if="column.dataKey === 'matchStatus'">
              <ListColumn
                :key="column.dataKey"
                field="matchStatus"
                title=""
                headerIcon="filter-icon"
                :headerStyle="headerStyle"
                :style="{ textAlign: 'center' }"
                minWidth="30"
                :width="column.width || 30"
                :disableColumnResize="true"
                :hide="column.hidden"
                :disableHover="true"
                :disableHeaderHover="true"
              >
                <template #headerCustomLayout="{ width, height }">
                  <Group
                    :width="width"
                    :height="height"
                    display="flex"
                    align-items="center"
                    justify-content="center"
                    :vue="{}"
                  >
                    <el-popover placement="bottom">
                      <el-checkbox-group v-model="state.filterMatchStatus" @change="onChangeMatchStatus()">
                        <el-checkbox :label="MatchStatusEnum.FULL_MATCH">完全匹配</el-checkbox>
                        <el-checkbox :label="MatchStatusEnum.EQUIVALENT_MATCH">等效匹配</el-checkbox>
                        <el-checkbox :label="MatchStatusEnum.NO_MATCH">未匹配</el-checkbox>
                      </el-checkbox-group>
                      <template #reference>
                        <div style="cursor: pointer; pointer-events: auto">
                          <ElIcon
                            class="cursor-pointer"
                            :class="{ 'filter-match-status': state.filterMatchStatus?.length > 0 }"
                          >
                            <Filter />
                          </ElIcon>
                        </div>
                      </template>
                    </el-popover>
                    <!-- <Image id="location1" :image="Filter" :width="15" :height="15" /> -->
                  </Group>
                </template>
                <template #customLayout="{ record, height, width }">
                  <Group
                    :height="height"
                    :width="width"
                    display="flex"
                    flexWrap="wrap"
                    :alignItems="'center'"
                    justifyContent="center"
                    :vue="{}"
                  >
                    <el-tooltip :content="MatchStatusEnumMapDesc[record.matchStatus]" placement="top">
                      <div style="cursor: pointer; pointer-events: auto">
                        <component v-if="record.matchStatus === 1" :is="toRaw(icon_matching)" />
                        <component v-if="record.matchStatus === 2" :is="toRaw(icon_quivalent_matching)" />
                        <component v-if="record.matchStatus === 3" :is="toRaw(icon_unmatched)" />
                        <span v-if="record.matchStatus === MatchStatusEnum.NOT_NEED_MATCH">--</span>
                      </div>
                    </el-tooltip>
                    <!-- <Image
                      v-if="record.matchStatus !== MatchStatusEnum.NOT_NEED_MATCH"
                      id="location"
                      :image="getMatchStatus(record)"
                      :width="15"
                      :height="15"
                      @mouseEnter="handleMatchStatusMoueEnter(row, col, record)"
                      @mouseLeave="handleMatchStatusMoueLeave()"
                    />
                    <Text v-else text="--" :fontSize="13" fontFamily="sans-serif" fill="black" /> -->
                  </Group>
                </template>
              </ListColumn>
            </template>
          </template>

          <ListColumn
            field="action"
            title="操作"
            :icon="['edit-icon', 'delete-icon']"
            :headerStyle="headerStyle"
            :style="{ textAlign: 'center' }"
            width="70"
            :disableColumnResize="true"
          />
        </ListTable>

        <div class="quotation-upload-container" v-if="!state.taskId">
          <div class="grid_header" />
          <el-upload
            class="w-[520px] h-[210px] quotation-upload"
            ref="uploadRef"
            drag
            :disabled="state.disabledUpload"
            :auto-upload="false"
            :limit="state.fileLimit"
            :multiple="state.multiple"
            :accept="state.accept"
            :on-exceed="onExceed"
            :on-change="onChangeUploadFile"
            :on-remove="onRemoveFile"
          >
            <el-icon class="upload-icon" :size="48"><upload-filled class="text-5xl" /></el-icon>
            <div>将询价单拖到此处，或 <span class="text-primary">点击上传</span></div>
          </el-upload>
          <div class="file" v-if="state.uploadRawFiles?.length">
            <el-scrollbar max-height="180px" class="w-full">
              <div
                class="flex items-center justify-between flex-1 gap-5"
                v-for="(item, index) in state.uploadRawFiles"
                :key="index"
              >
                <img v-if="item.type.startsWith('image/')" class="w-9 h-9" :src="ImageIcon" />
                <img v-else class="w-9 h-9" :src="ExcelIcon" />
                <div class="flex-1">
                  <div class="flex items-center gap-[10px] justify-between mr-[15px]">
                    <span class="file_name line-clamp-2 break-all">{{ item.name }}</span>
                    <el-icon @click="onDeleteUploadFile(index)" class="cursor-pointer" color="#F56C6C"
                      ><CircleCloseFilled
                    /></el-icon>
                  </div>
                  <el-progress class="flex-1" :percentage="state.uploadPercentage" />
                </div>
              </div>
            </el-scrollbar>

            <div class="flex flex-col">
              <ElButton
                :icon="CaretRight"
                :disabled="!Array.isArray(state.fileIds) || state.fileIds?.length === 0"
                type="primary"
                :loading="startParsingLoading"
                @click="handleStartParsing()"
                >开始解析</ElButton
              >
              <el-checkbox class="!h-6" v-model="state.isFromSGCC" label="国网" />
            </div>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-center quotation-amount" v-if="state.list.length">
        <div class="select-row flex items-center text-xs" v-if="selectedRows">
          <div class="py-1 rows">{{ selectedRows }}</div>
          <div class="px-3">
            <el-button
              v-if="selectAIRecommendRows"
              type="primary"
              text
              class="text-xs mx-1"
              size="small"
              @click="handleBatchConfirmApplySecondParse()"
            >
              批量应用AI推荐型号
            </el-button>
            <el-button
              type="primary"
              text
              class="text-xs mx-1"
              size="small"
              @click="onToggleQuotationBatchEditVisible()"
            >
              批量编辑
            </el-button>
            <el-button text type="danger" class="text-xs mx-1" size="small" @click="onDeleteSelectedRows()">
              删除
            </el-button>
          </div>
          <div class="clear" @click="onToggleSelectedAll(false)">
            <el-icon><Close /></el-icon>
          </div>
        </div>
        <div v-else class="text-xs font-semibold">
          含税合计总价：{{ (state.quotationInquiryProgress?.quotationAmount || 0).toLocaleString() }}
        </div>
      </div>
    </div>
    <QuotationDiscountDialog
      :taskId="state.taskId"
      v-model="state.quotationDiscountDialogVisible"
      @onSaveSuccess="handleBatchSetDiscount()"
    />
    <PasteSpecificationDialog
      v-model="state.pasteSpecificationDialogVisible"
      @onAnalysisSuccess="handleAnalysisSuccess($event)"
    />
    <QuotationRowEditDialog
      v-model="state.quotationRowEditDialogVisible"
      :quotationInquiryRow="state.quotationInquiryRow"
      :is-add="state.isAddModelNameSpecification"
      :taskId="state.taskId"
      @onSaveSuccess="handleuotationRowEditSuccess($event)"
      @onSaveSCancel="handleSaveSCancel()"
    />
    <PurchaseOrderSync />
    <QuotationDisplayColumn v-model="state.setDisplayColumnVisible" @onSetColumn="handleOnSetColumn()" />
    <QuotationBatchEdit
      v-model="state.quotationBatchEditVisible"
      :ids="selectedRowIds"
      @onSaveSuccess="handleBatchEditSaveSuccess()"
      @onCancel="onToggleSelectedAll(false)"
    />
    <div ref="headerFilterContainer">
      <HeaderFilter
        :keyword="state.searchKeyword[headerFilterColumnField]"
        v-model="state.headerFilterVisible"
        @onSearch="handleOnSearchByKeyWord($event, headerFilterColumnField)"
      />
      <HeaderModelNameFilter
        :specification-column="specificationColumn"
        :voltage-level-column="voltageLevelColumn"
        @onSearch="handleOnSearchByModelName($event)"
        v-model="state.headerFilterModelNameVisible"
      />
    </div>
    <div ref="filterMatchStatusContainer">
      <el-popover placement="bottom" :visible="state.filterMatchStatusContainerVisible">
        <el-checkbox-group v-model="state.filterMatchStatus" @change="onChangeMatchStatus()">
          <el-checkbox :label="MatchStatusEnum.FULL_MATCH">完全匹配</el-checkbox>
          <el-checkbox :label="MatchStatusEnum.EQUIVALENT_MATCH">等效匹配</el-checkbox>
          <el-checkbox :label="MatchStatusEnum.NO_MATCH">未匹配</el-checkbox>
        </el-checkbox-group>
        <template #reference>
          <div />
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script setup lang="ts" name="inquiry-unit">
import { queryInquiryOrganizationList } from "@/api/basic/inquiry-organization";
import { getLatestVersion } from "@/api/product-version";
import {
  batchDeleteTaskItemByIds,
  deleteTaskItemById,
  getParseDetailByTaskItemId,
  getQuotationInquiryCompletedParseList,
  getQuotationInquiryProgress,
  quotationInquiryParseInquiry
} from "@/api/quotation/quotation-inquiry";
import { uploadFile } from "@/api/upload-file";
import ExcelIcon from "@/assets/img/excel-icon.png";
import ImageIcon from "@/assets/img/image_icon.png";
import {
  MatchStatusEnum,
  MatchStatusEnumMapDesc,
  QuotationInquiryParseEnum,
  SecondParseStatusEnum,
  TaskParseStatusEnum
} from "@/enums";
import {
  IInquiryOrganization,
  IQuotationInquiryDetail,
  IQuotationInquiryExport,
  IQuotationInquiryProgress
} from "@/models";
import { useConfirm } from "@/utils/useConfirm";
import { useLoadingFn } from "@/utils/useLoadingFn";
import PasteSpecificationDialog from "@/views/components/paste-specification/dialog.vue";
import QuotationDiscountDialog from "@/views/components/quotation-discount/dialog.vue";
import {
  CaretRight,
  CircleCloseFilled,
  Discount,
  Plus,
  UploadFilled,
  CopyDocument,
  Close,
  Setting,
  Filter
} from "@element-plus/icons-vue";
import { Arrayable, useIntervalFn, useEventListener } from "@vueuse/core";
import { Column, ElButton, ElMessage, genFileId, UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { computed, onMounted, reactive, ref, toRaw, nextTick, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useColumns } from "./columns";
import QuotationHeader from "./quotation-header.vue";
import QuotationRowEditDialog from "@/views/components/quotation-row-edit/dialog.vue";
import { cloneDeep, storageLocal } from "@pureadmin/utils";
import type { CheckboxValueType } from "element-plus";
import icon_matching from "@/assets/svg/icon_matching.svg?component";
import icon_quivalent_matching from "@/assets/svg/icon_quivalent_matching.svg?component";
import icon_unmatched from "@/assets/svg/icon_unmatched.svg?component";
// import Filter from "@/assets/svg/filter.svg?url";
import { PermissionKey, storageLocalForQuotationSetColumns } from "@/consts";
import PurchaseOrderSync from "@/components/purchase-order-sync/index.vue";
import { usePurchaseOrderSyncStore } from "@/store/modules";
import EditNumberCell from "@/views/components/quotation/cell/edit-number-cell.vue";
import EditStringCell from "@/views/components/quotation/cell/edit-string-cell.vue";
import { useSecondParseHook } from "./hook/second-parse-hook";
import QuotationDisplayColumn from "@/views/components/quotation/display-column/index.vue";
import { useUserStore } from "@/store/modules/user";
import { modelName, specification, voltageLevel } from "@/views/components/quotation/display-column/display-column";
import ModelNameCell from "./cell/model-name.vue";
import QuotationBatchEdit from "@/views/components/quotation/quotation-batch-edit/dialog.vue";
import HeaderFilter from "./header/filter.vue";
import HeaderModelNameFilter from "./header/model-name-filter.vue";
import { ListTable, ListColumn, Text, Group, Tag } from "@visactor/vue-vtable";
import { useTable } from "./hook/table.hook";
import { supportAllFileTypes, supportedFileTypes, supportImageFileTypes } from "./const";
import { formatThousands } from "@/utils/format";

interface IQuotationInquiryDetailExt extends IQuotationInquiryDetail {
  selected?: boolean;
}

const listTableRef = ref<InstanceType<typeof ListTable>>();
const { columns } = useColumns();
const {
  tableOption,
  registerIcon,
  filterIcon,
  filterIconActivate,
  editIcon,
  deleteIcon,
  filterIconModelName,
  filterIconModelNameActivate
} = useTable();
let allQuotationList: Array<IQuotationInquiryDetailExt> = [];
const router = useRouter();
const route = useRoute();
const { pause: stopPollingQuotationInquiry, resume: startPollingQuotationInquiry } = useIntervalFn(
  pollingQuotationInquiry,
  1800,
  {
    immediate: false
  }
);

const syncStore = usePurchaseOrderSyncStore();
const userStore = useUserStore();
const { onApplyAiRecommendModel, onBatchConfirmApplySecondParse, applyAiRecommendModelLoading } = useSecondParseHook(
  handleGetQuotationInquiryParseDetailList
);

const headerStyle: Record<string, string | number> = { fontSize: 14, color: "#606266", bgColor: "#f5f7fa" };

const uploadRef = ref<UploadInstance>();
const columnsRef = ref<Array<Column>>([]);
const startParsingLoading = ref();
const headerFilterContainer = ref<HTMLDivElement>();
let parseSchedule = 0;
let versionId: string;
let headerFilterColumnField: string;

const state = reactive<{
  list: Array<IQuotationInquiryDetailExt>;
  quotationDiscountDialogVisible: boolean;
  pasteSpecificationDialogVisible: boolean;
  quotationRowEditDialogVisible: boolean;
  quotationInquiryExport: IQuotationInquiryExport;
  inquiryOrganizationList: Array<IInquiryOrganization>;
  uploadRawFiles: Array<UploadRawFile | File>;
  taskId?: string;
  uploadPercentage: number;
  quotationInquiryProgress: IQuotationInquiryProgress;
  fileId?: string;
  fileIds?: Array<string>;
  toggleOriginalRow: boolean;
  toggleDiscount: boolean;
  quotationInquiryRow: IQuotationInquiryDetail;
  isAddModelNameSpecification: boolean;
  isFromSGCC: boolean;
  filterMatchStatus: Array<MatchStatusEnum>;
  accept: string;
  fileLimit: number;
  disabledUpload: boolean;
  multiple: boolean;
  setDisplayColumnVisible: boolean;
  quotationBatchEditVisible: boolean;
  searchKeyword: Record<string, string>;
  selectedList: Array<IQuotationInquiryDetailExt>;
  headerFilterVisible: boolean;
  headerFilterModelNameVisible: boolean;
  modelNameColumnWidth: number;
  filterMatchStatusContainerVisible: boolean;
}>({
  list: [],
  quotationDiscountDialogVisible: false,
  pasteSpecificationDialogVisible: false,
  quotationInquiryExport: {},
  inquiryOrganizationList: [],
  uploadRawFiles: [],
  taskId: "",
  uploadPercentage: 0,
  quotationInquiryProgress: {},
  toggleOriginalRow: true,
  toggleDiscount: false,
  quotationRowEditDialogVisible: false,
  quotationInquiryRow: {},
  isAddModelNameSpecification: false,
  isFromSGCC: false,
  filterMatchStatus: [],
  accept: supportAllFileTypes,
  fileLimit: 1,
  fileIds: [],
  disabledUpload: false,
  multiple: false,
  setDisplayColumnVisible: false,
  quotationBatchEditVisible: false,
  searchKeyword: {},
  selectedList: [],
  headerFilterVisible: false,
  headerFilterModelNameVisible: false,
  modelNameColumnWidth: 150,
  filterMatchStatusContainerVisible: false
});

const disabledEditDiscount = computed(
  () =>
    state.quotationInquiryProgress?.parseStatus !== TaskParseStatusEnum.PARSED ||
    state.quotationInquiryProgress?.useSecondParse !== SecondParseStatusEnum.PARSED
);
const selectedRows = computed(() => state.selectedList.length);
const selectedRowIds = computed(() => state.selectedList.map(x => x.id));
const selectAIRecommendRows = computed(
  () =>
    state.selectedList.filter(x => x.secondParseStatus === SecondParseStatusEnum.PARSED).length &&
    !columnsRef.value?.find(x => x.dataKey === "aiRecommendModel")?.hidden
);

const voltageLevelColumn = computed(() => columnsRef.value.find(x => x.key === voltageLevel));
const specificationColumn = computed(() => columnsRef.value.find(x => x.key === specification));

const handleStartParsing = useLoadingFn(onStartParsing, startParsingLoading);

onMounted(() => {
  registerIcon?.();
  handleOnSetColumn();
  if (route.query?.taskId) {
    state.taskId = route.query.taskId as string;
    pollingQuotationInquiry();
    startPollingQuotationInquiry();
  }

  handleGetLatestVersion();
  handleQueryInquiryOrganization();

  useEventListener("paste" as Arrayable<keyof WindowEventMap>, event => {
    // 如果存在TaskId 则不运行粘贴
    if (state.taskId) {
      return;
    }
    const clipboardEvent = event as ClipboardEvent;
    if (clipboardEvent.clipboardData && clipboardEvent.clipboardData?.files?.length > 0) {
      const file = clipboardEvent.clipboardData.files[0];
      // 图片，pdf,excel 粘贴
      const fileType = file.type;
      if (file && (file.type.startsWith("image/") || supportedFileTypes.includes(fileType))) {
        // state.uploadRawFiles = [file];
        onSelectUploadFile(file);
      } else {
        ElMessage.warning("不支持粘贴该文件类型");
      }
    }
  });
  tableInstanceListenEvents();
});

onUnmounted(() => {
  listTableRef.value?.vTableInstance.off("click_cell");
  listTableRef.value?.vTableInstance.off("icon_click");
  listTableRef.value?.vTableInstance.off("resize_column_end");
});

const onToggleSelectedAll = (selected: CheckboxValueType) => {
  state.list = state.list.map(item => {
    item.selected = selected as boolean;
    return item;
  });
  state.selectedList = [];
};

const onDeleteSelectedRows = async () => {
  if (state.selectedList.length === 0) {
    ElMessage.warning("请选择要删除的行");
    return;
  }

  if (!(await useConfirm("确认删除后，数据将无法恢复", "确认删除"))) {
    return;
  }
  const ids = state.selectedList.map(x => x.id);
  await batchDeleteTaskItemByIds(ids);
  state.list = state.list.filter(x => !ids.includes(x.id));
  state.selectedList = [];
  allQuotationList = allQuotationList.filter(x => !ids.includes(x.id));
  handleQuotationInquiryProgress();
  ElMessage.success("删除成功");
};

const handleBatchConfirmApplySecondParse = async () => {
  onBatchConfirmApplySecondParse(state.selectedList.filter(x => x.secondParseStatus == SecondParseStatusEnum.PARSED));
};

// // 根据关键词搜索
const handleOnSearchByKeyWord = (keyword: string, field: string) => {
  if (field === "aiRecommendModel") {
    ["secondParseModelName", "secondParseVoltageLevel", "secondParseSpecification"].forEach(key => {
      state.searchKeyword[key] = keyword;
    });
  }
  state.searchKeyword[field] = keyword;

  handleSearch();
};

// 型号搜索
const handleOnSearchByModelName = (params: { modelName?: string; voltageLevel?: string; specification?: string }) => {
  if (!params || Object.keys(params).length === 0) {
    return;
  }

  state.searchKeyword.modelName = params.modelName;

  if (voltageLevelColumn.value?.voltageLevelMergeModelName) {
    state.searchKeyword.voltageLevel = params.voltageLevel;
  }

  if (specificationColumn.value?.specificationMergeModelName) {
    state.searchKeyword.specification = params.specification;
  }

  handleSearch();
};

const handleSearch = () => {
  if (Object.keys(state.searchKeyword).length === 0) {
    state.list = cloneDeep(allQuotationList);
    return;
  }

  const aiRecommendModelArr: Array<string> = [
    "secondParseModelName",
    "secondParseVoltageLevel",
    "secondParseSpecification"
  ];
  state.list = cloneDeep(
    allQuotationList.filter(x => {
      return Object.keys(state.searchKeyword)
        .filter(x => x !== "aiRecommendModel")
        .every(key => {
          if (aiRecommendModelArr.includes(key)) {
            return aiRecommendModelArr.some(field => x[field]?.includes(state.searchKeyword[field]));
          }
          return x[key]?.includes(state.searchKeyword[key]);
        });
    })
  );
};

const onChangeMatchStatus = () => {
  if (!Array.isArray(state.filterMatchStatus) || state.filterMatchStatus.length === 0) {
    state.list = cloneDeep(allQuotationList);
  } else {
    state.list = cloneDeep(allQuotationList.filter(x => state.filterMatchStatus.includes(x.matchStatus)));
  }
};

const onToggleDispalyColumnVisible = () => {
  state.setDisplayColumnVisible = true;
};

const onToggleQuotationBatchEditVisible = () => {
  state.quotationBatchEditVisible = true;
};

const handleOnSetColumn = async () => {
  const profile = await userStore.getProfile;
  let setColumns: Array<Column> = storageLocal().getItem(storageLocalForQuotationSetColumns);

  if (!profile?.tenantInfo?.quotationSecondConfirm) {
    const excludeFileds = ["aiRecommendModel", "aiParseModel"];
    setColumns = setColumns?.filter(x => !excludeFileds.includes(x.dataKey as string));
  }

  const displayColumns = Array.isArray(setColumns) && setColumns.length ? setColumns : columns;
  const voltageLevelMergeModelName = displayColumns?.find(x => x.key === voltageLevel)?.voltageLevelMergeModelName;
  const specificationMergeModelName = displayColumns?.find(x => x.key === specification)?.specificationMergeModelName;
  if (voltageLevelMergeModelName && specificationMergeModelName) {
    const modelNameColumn = displayColumns?.find(x => x.key === modelName);
    if (modelNameColumn) {
      state.modelNameColumnWidth = 260;
    }
  }

  columnsRef.value = displayColumns;
  tableOption.value.frozenColCount = displayColumns?.filter(x => x.preFreeze && !x.hidden).length + 1;
  tableOption.value.rightFrozenColCount = displayColumns?.filter(x => x.afterFreeze && !x.hidden).length + 1;
  listTableRef.value?.vTableInstance.renderWithRecreateCells();
  listTableRef.value?.vTableInstance.updateOption(tableOption);
};

const onExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  // state.uploadRawFiles = [file];
  uploadRef.value!.handleStart(file);
};

const onChangeUploadFile: UploadProps["onChange"] = uploadFile => {
  onSelectUploadFile(uploadFile.raw);
};

/**  处理上传图片 */
function onSelectUploadFile(file: File) {
  state.disabledUpload = true;
  const imageType = file.type.startsWith("image/");
  if (imageType && state.uploadRawFiles.some(item => !item.type.startsWith("image/"))) {
    state.uploadRawFiles = [];
  }
  state.multiple = imageType;
  if (imageType) {
    state.accept = supportImageFileTypes;
    state.fileLimit = 10;
    state.uploadRawFiles.push(file);
  } else {
    state.accept = supportAllFileTypes;
    state.fileLimit = 1;
    state.uploadRawFiles = [file];
  }
  handleUploadQuotationInquiryFile(state.uploadRawFiles[state.uploadRawFiles.length - 1], imageType);
}

const onRemoveFile: UploadProps["onRemove"] = () => {
  state.uploadRawFiles = [];
  state.fileId = null;
};

const onDeleteUploadFile = async (index: number) => {
  if (!(await useConfirm("删除文件后，请重新上传新的询价单", "确认删除询价单"))) {
    return;
  }

  state.uploadRawFiles.splice(index, 1);
  state.fileIds.splice(index, 1);
  if (!Array.isArray(state.uploadRawFiles) || state.uploadRawFiles?.length === 0) {
    state.accept = supportAllFileTypes;
    state.uploadPercentage = 0;
    state.fileIds = [];
  }
};

const handleBatchSetDiscount = () => {
  handleGetQuotationInquiryParseDetailList();
  handleQuotationInquiryProgress();
};

const onEditQuotationRowShow = (data: IQuotationInquiryDetail) => {
  state.quotationInquiryRow = data;
  state.isAddModelNameSpecification = false;
  state.quotationRowEditDialogVisible = true;
};

const onAddModelNameSpecificationDialogVisible = () => {
  state.quotationInquiryRow = {};
  state.isAddModelNameSpecification = true;
  state.quotationRowEditDialogVisible = true;
};

const onDeleteQuotationRow = async (id: string) => {
  if (!(await useConfirm("确认删除该行报价？", "确认删除"))) {
    return;
  }

  await deleteTaskItemById(id);
  state.list = state.list.filter(x => x.id !== id);
  allQuotationList = allQuotationList.filter(x => x.id !== id);
  handleQuotationInquiryProgress();
  ElMessage.success("删除成功");
};

const handleuotationRowEditSuccess = (data: IQuotationInquiryDetail) => {
  if (state.isAddModelNameSpecification) {
    state.list.push(data);
    allQuotationList = cloneDeep(state.list);
    scrollToRow(state.list.length);
  } else {
    handleGetQuotationInquiryParseDetailList();
  }
  handleQuotationInquiryProgress();
};

const handleBatchEditSaveSuccess = () => {
  handleGetQuotationInquiryParseDetailList();
};

const handleSaveSCancel = () => {
  state.quotationInquiryRow = null;
};

async function handleOnEditSuccess(data: IQuotationInquiryDetail, fieldKey: string) {
  try {
    await handleGetParseDetailByTaskItemId(data);
    handleQuotationInquiryProgress();
  } catch (error) {
    fieldKey.split(",").forEach(key => {
      data[key] = allQuotationList.find(x => x.id === data.id)[key];
    });
  }
}

async function onStartParsing() {
  if (!Array.isArray(state.uploadRawFiles) || state.uploadRawFiles.length === 0) {
    ElMessage.warning("请选择报价单");
    return;
  }

  const { data: taskId } = await quotationInquiryParseInquiry({
    // inquiryFileId: state.fileId,
    inquiryFileIdList: state.fileIds,
    versionId,
    isFromSGCC: state.isFromSGCC,
    sourceType: QuotationInquiryParseEnum.WEB
  });
  state.taskId = taskId;
  replaceQueryParams({ taskId });
  state.accept = supportAllFileTypes;
  ElMessage.success("正在解析询价单");
  syncStore.syncAIAnalysis(state.taskId);
  pollingQuotationInquiry();
  startPollingQuotationInquiry();
}

function handleStopParsing() {
  stopPollingQuotationInquiry();
  state.quotationInquiryProgress.parseStatus = TaskParseStatusEnum.PARSING_STOP;
}

function handleonReParseing() {
  startPollingQuotationInquiry();
  state.quotationInquiryProgress.parseStatus = TaskParseStatusEnum.PARSING;
}

//function handleUploadFile(uploadRawFile: UploadRawFile) {
//  state.uploadRawFiles = [uploadRawFile];
//  handleUploadQuotationInquiryFile(uploadRawFile);
// }

async function handleUploadQuotationInquiryFile(uploadRawFile: File, imageType = false): Promise<void> {
  state.fileId = null;
  const formData = new FormData();
  formData.append("file", uploadRawFile);
  const uploadInterval = setInterval(() => {
    if (state.uploadPercentage <= 95) {
      state.uploadPercentage += Math.floor(Math.random() * 5) + 1;
      return;
    }
  }, 300);

  const { data } = await uploadFile(formData).finally(() => (state.disabledUpload = false));

  clearInterval(uploadInterval);
  if (!data?.id) {
    ElMessage.error("上传报价单文件失败，请重新上传");
    // state.fileId = null;
    return;
  }
  if (!imageType) {
    state.fileIds = [data.id];
  } else {
    state.fileIds.push(data.id);
  }
  state.uploadPercentage = 100;
}

const onBatchQuotationDiscount = () => {
  state.quotationDiscountDialogVisible = true;
};

const onPasteSpecification = () => {
  state.pasteSpecificationDialogVisible = true;
};

// 解析文本  成功
const handleAnalysisSuccess = (taskId: string) => {
  state.taskId = taskId;
  replaceQueryParams({ taskId });
  ElMessage.success("正在解析询价单");
  state.list = [];
  allQuotationList = [];
  pollingQuotationInquiry();
  startPollingQuotationInquiry();
};

const handleQueryInquiryOrganization = async () => {
  const { data } = await queryInquiryOrganizationList();
  state.inquiryOrganizationList = data;
};

async function pollingQuotationInquiry() {
  await handleGetQuotationInquiryProgress();
  await handleGetQuotationInquiryParseDetailList(true);
}

async function handleGetQuotationInquiryParseDetailList(scrollToBottom = false) {
  const { data } = await getQuotationInquiryCompletedParseList(state.taskId);
  state.selectedList = [];
  if (Array.isArray(data) && data.length) {
    state.list = data.map(item => ({ ...item, selected: false }));
    if (scrollToBottom) {
      scrollToRow(state.list.length);
    }

    allQuotationList = cloneDeep(state.list);
  } else {
    state.list = [];
    allQuotationList = [];
  }
}

async function handleGetQuotationInquiryProgress() {
  const { data } = await getQuotationInquiryProgress(state.taskId);
  const { parseStatus, useSecondParse } = state.quotationInquiryProgress;
  if (
    [TaskParseStatusEnum.PARSING_STOP].includes(parseStatus) ||
    (parseStatus === TaskParseStatusEnum.PARSED && useSecondParse === SecondParseStatusEnum.PARSED)
  ) {
    syncStore.sync.percentage = data.parseSchedule;
    stopPollingQuotationInquiry();
    await handleGetQuotationInquiryParseDetailList(true);
    //  scrollToRow(0);
    return;
  }
  if (data.parseSchedule === 0) {
    parseSchedule = parseSchedule += 4;
    parseSchedule = parseSchedule >= 10 ? 10 : parseSchedule;
    state.quotationInquiryProgress = { ...data, parseSchedule: parseSchedule };
  } else {
    data.parseSchedule = parseFloat((parseSchedule + data.parseSchedule).toFixed(2));
    state.quotationInquiryProgress = data;
  }
  syncStore.sync.percentage = state.quotationInquiryProgress.parseSchedule;
  if (data && data.parseStatus === TaskParseStatusEnum.PARSED && data.useSecondParse === SecondParseStatusEnum.PARSED) {
    stopPollingQuotationInquiry();
    await handleGetQuotationInquiryParseDetailList(true);
    // scrollToRow(0);
    ElMessage.success("解析完成");
  }
}

const handleGetLatestVersion = async () => {
  const { data } = await getLatestVersion();
  if (data?.id) {
    versionId = data.id;
  }
};

function replaceQueryParams(newParams) {
  router.replace({
    query: {
      ...router.currentRoute.value.query,
      ...newParams
    }
  });
}
const handleGetParseDetailByTaskItemId = async (data: IQuotationInquiryDetail) => {
  const { data: updateResult } = await getParseDetailByTaskItemId(data.id);
  data = Object.assign(data, updateResult);
  let quotationItem = allQuotationList.find(x => x.id === data.id);
  quotationItem.id = data.id;
  quotationItem = Object.assign(quotationItem, updateResult);
};

function scrollToRow(rows: number): void {
  nextTick(() => {
    listTableRef.value?.vTableInstance?.scrollToCell({ row: rows });
  });
}
const handleQuotationInquiryProgress = async () => {
  const { data } = await getQuotationInquiryProgress(state.taskId);
  state.quotationInquiryProgress = data;
};

const tableInstanceListenEvents = () => {
  onTableCellClick();
  onTableTconClick();
  onResizeColumnEnd();
};

const onTableCellClick = () => {
  listTableRef.value?.vTableInstance.on("click_cell", (args: Record<string, any>) => {
    const { targetIcon, originData, cellType, field } = args;
    if (targetIcon?.name === filterIcon) {
      return;
    }

    if (targetIcon?.name === editIcon && !disabledEditDiscount.value) {
      onEditQuotationRowShow(originData);
      return;
    }

    if (targetIcon?.name === deleteIcon && !disabledEditDiscount.value) {
      onDeleteQuotationRow(originData.id);
      return;
    }

    if (cellType === "checkbox" && field === "checkbox") {
      onTableToggleCheckbox(args);
    }
  });
};
const onResizeColumnEnd = () => {
  listTableRef.value?.vTableInstance.on("resize_column_end", (args: Record<string, any>) => {
    const width = listTableRef.value?.vTableInstance.getColWidth(args.col);
    const headerFilterColumnField = listTableRef.value?.vTableInstance.getHeaderField(args.col, 0);
    columnsRef.value = columnsRef.value.map(item => {
      if (item.dataKey === headerFilterColumnField) {
        item.width = width;
      }
      return item;
    });
  });
};

const onTableToggleCheckbox = (args: Record<string, any>) => {
  const { col, row, originData, cellLocation } = args;

  if (state.list?.length === 0) {
    return;
  }

  if (cellLocation === "body") {
    const checked: boolean = listTableRef.value.vTableInstance.getCellCheckboxState(col, row);

    if (checked) {
      if (!state.selectedList.some(x => x.id === originData.id)) {
        state.selectedList.push(originData);
      }
      return;
    }

    state.selectedList = state.selectedList.filter(x => x.id !== originData.id);
    return;
  }

  if (cellLocation === "columnHeader") {
    const checked = listTableRef.value.vTableInstance.getCellCheckboxState(col, 1);
    state.selectedList = checked ? state.list : [];
  }
};

const onTableTconClick = () => {
  listTableRef.value?.vTableInstance.on("icon_click", (args: Record<string, any>) => {
    const { name } = args;
    if ([filterIcon, filterIconActivate].includes(name)) {
      handleTablefilterIconEvent(args);
      return;
    }

    if ([filterIconModelName, filterIconModelNameActivate].includes(name)) {
      handleTablefilterModelNameIconEvent(args);
    }
  });
};

function handleTablefilterIconEvent(args: Record<string, any>) {
  const vTableInstance = listTableRef.value.vTableInstance;
  if (state.headerFilterVisible) {
    state.headerFilterVisible = false;
    return;
  }
  const { col, row } = args;
  headerFilterColumnField = vTableInstance.getHeaderField(col, row);
  positionHeaderFilterContainer(args);
  state.headerFilterVisible = true;
}

function handleTablefilterModelNameIconEvent(args: Record<string, any>) {
  if (state.headerFilterModelNameVisible) {
    state.headerFilterModelNameVisible = false;
    return;
  }
  positionHeaderFilterContainer(args);
  state.headerFilterModelNameVisible = true;
}

function positionHeaderFilterContainer(args: Record<string, any>) {
  const vTableInstance = listTableRef.value.vTableInstance;
  const filterContainer = vTableInstance.getElement();
  const { col, row } = args;
  const positonRect = vTableInstance.getCellRelativeRect(col, row);
  headerFilterContainer.value.style.position = "absolute";
  headerFilterContainer.value.style.padding = "4px";
  headerFilterContainer.value.style.width = "100%";
  headerFilterContainer.value.style.boxSizing = "border-box";
  filterContainer.appendChild(headerFilterContainer.value);
  headerFilterContainer.value.style.top = positonRect.top + positonRect.height + "px";
  headerFilterContainer.value.style.left = positonRect.left + "px";
  headerFilterContainer.value.style.width = positonRect.width + "px";
  headerFilterContainer.value.style.height = positonRect.height + "px";
}

function getModelName(record: IQuotationInquiryDetailExt) {
  let modelName: string = record.modelName;
  if (
    voltageLevelColumn.value &&
    voltageLevelColumn.value?.hidden &&
    voltageLevelColumn.value.voltageLevelMergeModelName &&
    record.voltageLevel
  ) {
    modelName = `${modelName}-${record.voltageLevel}`;
  }

  if (
    specificationColumn.value &&
    specificationColumn.value?.hidden &&
    specificationColumn.value.specificationMergeModelName &&
    record.specification
  ) {
    modelName = `${modelName}-${record.specification}`;
  }

  return modelName;
}

function getPriceMatchRecordColorByIsMatchPrice(args: Record<string, any>) {
  const vTableInstance = listTableRef.value.vTableInstance;
  const rowValue: IQuotationInquiryDetailExt = vTableInstance.getRecordByCell(args.col, args.row);
  return !rowValue?.isMatchPrice ? "#e6a23c" : "black";
}
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>

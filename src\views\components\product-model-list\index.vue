<template>
  <div class="flex flex-col h-full">
    <el-input class="mb-4" placeholder="产品型号" :suffix-icon="Search" v-model="state.keyword" />
    <el-scrollbar class="w-full flex-1">
      <div class="flex flex-col gap-2">
        <div
          class="text-base cursor-pointer category-item"
          :class="{ 'activate-dict': state.selectProductModel?.id === item.id }"
          v-for="item in state.productModels"
          :key="item.id"
          @click="onSelectProductModel(item)"
        >
          {{ item.modelName }}
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { IProductModel } from "@/models";
import { reactive, watch } from "vue";
import { cloneDeep } from "@pureadmin/utils";
import { queryAllProductModel } from "@/api/product-model";

defineExpose({ emptySelectProductModel, handleQueryAllProductModel });

const emits = defineEmits<{
  (e: "onSelectProductModel", value: IProductModel): void;
}>();

const props = withDefaults(
  defineProps<{
    defaultSelected?: boolean;
    selectedCategoryId?: string;
  }>(),
  {
    defaultSelected: false
  }
);

let allProductModel: Array<IProductModel> = [];
const state = reactive<{
  productModels: Array<IProductModel>;
  selectProductModel?: IProductModel;
  keyword?: string;
}>({
  productModels: []
});

watch(
  () => state.keyword,
  keyword => {
    state.productModels = keyword?.trim()
      ? allProductModel.filter(x => x.modelName?.toLocaleLowerCase().includes(keyword.toLocaleLowerCase()))
      : allProductModel;
  }
);

watch(
  () => props.selectedCategoryId,
  selectedCategoryId => {
    if (!selectedCategoryId) {
      state.productModels = [];
    } else {
      handleQueryAllProductModel();
    }
  }
);

async function handleQueryAllProductModel() {
  const { data } = await queryAllProductModel(props.selectedCategoryId);
  state.productModels = data;
  allProductModel = cloneDeep(data);

  if (state.selectProductModel) {
    return;
  }

  if (props.defaultSelected && Array.isArray(data) && data.length) {
    onSelectProductModel(data[0]);
  }
}

const onSelectProductModel = (productModel: IProductModel) => {
  state.selectProductModel = productModel;
  emits("onSelectProductModel", productModel);
};

function emptySelectProductModel() {
  state.selectProductModel = undefined;
}
</script>

<style scoped lang="scss">
.category-item {
  @apply text-base text-regular p-0.5 pl-2 flex-bc;

  &:hover {
    color: var(--el-color-primary);
    background: var(--el-fill-color-light);

    .edit-icon {
      visibility: visible;
    }
  }

  &.activate-dict {
    color: var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
  }
}
</style>

.party {
  .el-form-item {
    margin-bottom: 0.55rem;
    //  margin-right: 0;

    &.copper-price {
      :deep(.el-input__wrapper) {
        padding-right: 0;
        box-shadow: 0.0625rem 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 0 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 -0.0625rem 0 0 var(--el-input-border-color, var(--el-border-color)) inset,
          0 0.0625rem 0 0 var(--el-input-border-color, var(--el-border-color)) inset;
      }

      :deep(.el-input-group__append) {
        padding: 0 10px 0 3px;
        background-color: transparent !important;
      }
    }

    &.inquiry-organization {
      :deep(.el-form-item__label) {
        font-weight: bold !important;
      }
    }

    &.show-original-row {
      width: 100%;

      :deep(.el-form-item__label) {
        white-space: nowrap;
      }

      :deep(.el-form-item__content) {
        justify-content: end;
      }
    }
  }
}

.risk-tip {
  background-color: var(--el-color-danger-light-9);
  padding: 8px 20px;

  .tip,
  .link {
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0;
    color: var(--el-color-danger);
  }

  .link {
    color: var(--el-color-primary);
    margin-left: 4px;
    cursor: pointer;
    border-bottom: 2px solid var(--el-color-primary);
  }

  .close {
    width: 30px;
    text-align: right;

    .el-icon {
      svg {
        color: var(--el-color-danger);
      }
    }
  }
}

.quotation-grid {
  :deep(.el-table-v2__empty) {
    margin-top: 20px;
    height: calc(100% - 70px) !important;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f5;
  }
}

.quotation-upload {
  :deep(.el-upload) {
    height: 100%;

    .el-upload-dragger {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }

  :deep(.el-upload-list) {
    display: none;
  }
}

.upload-icon {
  color: var(--el-color-primary);
}

.file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e4e7ed;
  width: 520px;
  margin-top: 10px;
  padding: 10px 20px 10px 20px;
  background-color: #ffffff;
  gap: 30px;

  .file_name {
    font-size: 16px;
    font-weight: 500;
    color: #303339;
  }
}

.action-group {
  width: 100%;

  .action-item {
    flex: 1;
    text-align: center;
    cursor: pointer;

    svg {
      color: #606266;
    }
  }
}

:deep(.cx-quotation_header) {
  background-color: #fafbfc;

  .el-table-v2__header-cell {
    background-color: #fafbfc;
    color: #303339;
    font-size: 14px;
    font-weight: 500;

    .filter {
      visibility: hidden;
    }

    &:hover {
      .filter {
        visibility: visible;
        // color: var(--el-color-primary);
      }
    }
  }
}

:deep(.cx-quotation_header-original-row) {
  .el-table-v2__header-cell[data-key="excelSheet"],
  .el-table-v2__header-cell[data-key="excelRowIndex"],
  .el-table-v2__header-cell[data-key="columnRawContent"] {
    background-color: var(--el-color-warning-light-9);
  }
}

:deep(.none-quotation) {
  opacity: 35%;
}

.discount-price {
  :deep(.el-input__suffix) {
    svg {
      height: 22px;
    }
  }
}

.filter-match-status {
  color: var(--el-color-primary);
}

.quotation-amount {
  margin-top: 4px;
  // border: 1px solid #dde0e6;
  padding: 4px 0;
  border-radius: 3px;

  .select-row {
    display: flex;
    width: -webkit-max-content;
    width: max-content;
    height: 44px;
    line-height: 44px;
    background: #fff;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    box-shadow: 0 4px 42px rgba(0, 0, 0, 0.15);

    .rows {
      background-color: var(--el-color-primary);
      color: #fff;
      padding: 0 18px;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }

    .clear {
      cursor: pointer;
      display: flex;
      align-items: center;
      border-left: 1px solid #dde0e6;
      padding: 0 18px;
      border-top-right-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }
}

.match-no-price {
  color: var(--el-color-warning);
}

.table-container {
  position: relative;

  .quotation-upload-container {
    position: absolute;
    top: 50px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f4f4f5;

    .grid_header {
      position: absolute;
      top: -50px;
      width: 100%;
      height: 50px;
      background-color: #ffffff;
      opacity: 35%;
    }
  }
}

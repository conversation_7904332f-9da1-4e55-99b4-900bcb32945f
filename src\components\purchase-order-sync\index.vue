<template>
  <el-dialog v-model="visible" width="56%" destroy-on-close class="ai-analysis-dialog" :close-on-press-escape="false">
    <template #header>
      <div class="dialog-header">
        <div class="dialog-title">AI识别</div>
        <button v-if="!running" class="close-btn" @click="closeDialog"><i class="el-icon-close" /></button>
      </div>
    </template>

    <div class="dialog-container">
      <!-- <template v-if="visible">
        <WordCloud ref="wordCloudContainer" />
      </template> -->

      <WordCloud ref="wordCloudContainer" />

      <div class="w-[800px] h-[140px] m-auto flex flex-col items-center gap-[10px] px-4 mt-5">
        <WaveProgress class="top-[30px]" :percentage="percentage" :status="progressStatus" />

        <StepScale />

        <el-progress
          class="w-full"
          :text-inside="true"
          :stroke-width="18"
          :percentage="percentage"
          :status="progressStatus"
        >
          <span />
        </el-progress>
        <div class="description" v-if="percentage >= 100">
          <div class="tips">{{ tips }}</div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <img :src="DialogFooterImage" alt="dialog-footer-img" class="footer-image" />
        <div class="footer-actions">
          <el-button size="large" :disabled="percentage != 100" :round="true" type="primary" @click="closeDialog">
            完成
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { PurchaseOrderSyncStatus } from "@/enums";
import { usePurchaseOrderSyncStore } from "@/store/modules";
import { computed, onMounted, onUnmounted, ref, watch } from "vue";
import { useNotification } from "./useNotification";

// 导入组件
import DialogFooterImage from "@/assets/img/dialog-footer.png";
import StepScale from "./step-scale.vue";
import WaveProgress from "./wave-progress/wave-progress.vue";
import WordCloud from "./word-cloud.vue";

const store = usePurchaseOrderSyncStore();
const { closeNotificationImmediate } = useNotification();

// 响应式变量
const visible = computed({
  get() {
    return store.dialogVisible;
  },
  set(value) {
    store.$patch({ dialogVisible: value });
  }
});

const shouldRenderContainer = ref<boolean>(false);
const wordCloudContainer = ref<InstanceType<typeof WordCloud>>();

// 计算属性
const running = computed(
  () =>
    store.sync.status === PurchaseOrderSyncStatus.RUNNING ||
    store.sync.status === PurchaseOrderSyncStatus.BACKGROUND_PULL
);
// const _success = computed(() => store.sync.status === PurchaseOrderSyncStatus.SUCCESS);
const fail = computed(() => store.sync.status === PurchaseOrderSyncStatus.FAIL);
const progressStatus = computed(() => (fail.value ? "exception" : ""));
const percentage = computed(() => (store.sync.percentage >= 100 ? 100 : store.sync.percentage));
const tips = computed(() => store.sync.tips);

watch(
  () => percentage.value,
  () => {
    if (percentage.value >= 100) {
      closeDialog();
    }
  }
);

// 方法
function openDialog(): void {
  visible.value = true;
  window.setTimeout(() => {
    shouldRenderContainer.value = true;
  }, 50);
}

function closeDialog(): void {
  if (wordCloudContainer.value) {
    wordCloudContainer.value.resetWordCloud();
  }
  shouldRenderContainer.value = false;
  visible.value = false;
}

function _reSync() {
  store.reSyncPurchaseOrder();
}

// function watchCloseNotification() {
//   let unwatch = whenever(
//     () => store.sync.status !== PurchaseOrderSyncStatus.RUNNING,
//     () => {
//       unwatch();
//       unwatch = null;
//       closeNotificationDelay();
//     }
//   );
// }

// 监听
watch(
  () => [store.dialogVisible, store.sync.status],
  ([visible, status]) => {
    if (!visible && status === PurchaseOrderSyncStatus.RUNNING) {
      //  openNotification();
      //  watchCloseNotification();
    }
    if (visible) {
      closeNotificationImmediate();
    }
  }
);

// 生命周期钩子
onMounted((): void => {
  // openDialog();
});

onUnmounted((): void => {
  // visible.value = false;
  //shouldRenderContainer.value = false;
});

// 对外暴露方法
defineExpose({
  openDialog,
  closeDialog
});
</script>

<style scoped lang="scss">
:deep(.el-progress-bar__inner) {
  $color: rgba(255, 255, 255, 0.2);
  background: repeating-linear-gradient(-60deg, $color 0, $color 2px, transparent 2px, transparent 8px) no-repeat bottom
    var(--el-color-primary);
}
</style>

<style lang="scss">
.el-notification__sync-purchase-order {
  --el-notification-width: 400px;

  .el-notification__group {
    width: 100%;
  }
}

.ai-analysis-dialog {
  border-radius: 20px;
  overflow: hidden;

  .dialog-header {
    position: relative;
    padding: 0;
    width: 100%;
    height: 100px;
    background: radial-gradient(
      38% 50% at 50% 0%,
      #a9f7ee 0%,
      rgba(203, 244, 255, 0.8151) 23%,
      rgba(255, 255, 255, 0) 100%
    );

    .dialog-title {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: var(--el-text-color-primary);
      font-size: 24px;
      font-weight: 500;
    }

    .close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      background: transparent;
      border: none;
      color: white;
      cursor: pointer;
    }
  }

  .dialog-container {
    position: relative;
  }

  .description {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 40px;
    margin-top: 20px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 14%,
      rgba(216, 238, 255, 0.5) 45%,
      rgba(216, 238, 255, 0.5) 55%,
      rgba(216, 238, 255, 0) 85%
    );

    .tips {
      color: var(--el-text-color-primary);
      font-size: 18px;
      font-weight: 500;
    }
  }

  .dialog-footer {
    position: relative;
    padding: 0;
    border-radius: 0 0 20px 20px;
    margin-top: -20px;

    .footer-image {
      width: 100%;
      height: 140px;
      border-radius: 0 0 20px 20px;
    }

    .footer-actions {
      position: absolute;
      bottom: 60px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 10px;

      .el-button {
        font-size: 18px;
        padding-left: 40px;
        padding-right: 40px;
      }
    }
  }
}
</style>
<style>
.ai-analysis-dialog .el-dialog__header {
  padding: 0;
  border-radius: 20px 20px 0 0;
  margin-right: 0;
}

.ai-analysis-dialog .el-dialog__footer {
  padding: 0;
  border-radius: 0 0 20px 20px;
}

.ai-analysis-dialog {
  border-radius: 20px;
}
</style>

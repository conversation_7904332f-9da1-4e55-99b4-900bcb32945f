import { Column } from "element-plus";
import { FixedDir } from "element-plus/es/components/table-v2/src/constants";

export function useColumns() {
  const columns: Array<Column> = [
    // {
    //   title: "",
    //   key: "selection",
    //   dataKey: "selection",
    //   width: 30,
    //   align: "center",
    //   fixed: FixedDir.LEFT
    // },
    {
      title: "序号",
      key: "rowIndex",
      dataKey: "rowIndex",
      width: 60,
      align: "center",
      preFreeze: true,
      fixed: FixedDir.LEFT
    },
    {
      title: "原始位置",
      key: "excelRowIndex",
      dataKey: "excelRowIndex",
      width: 90,
      fixed: FixedDir.LEFT,
      preFreeze: true,
      hidden: false
    },
    {
      title: "原始数据",
      key: "columnRawContent",
      dataKey: "columnRawContent",
      width: 220,
      fixed: FixedDir.LEFT,
      preFreeze: true,
      hidden: false
    },
    {
      title: "型号",
      key: "modelName",
      dataKey: "modelName",
      width: 0,
      preFreeze: true,
      fixed: FixedDir.LEFT
    },
    {
      title: "电压",
      key: "voltageLevel",
      dataKey: "voltageLevel",
      width: 100,
      fixed: FixedDir.LEFT,
      hidden: true,
      preFreeze: true,
      voltageLevelMergeModelName: true
    },
    {
      title: "规格",
      key: "specification",
      dataKey: "specification",
      width: 100,
      preFreeze: true,
      fixed: FixedDir.LEFT,
      voltageLevelMergeModelName: false
    },
    // {
    //   title: "AI提取型号",
    //   key: "aiParseModel",
    //   dataKey: "aiParseModel",
    //   width: 120,
    //   fixed: FixedDir.LEFT,
    //   hidden: true
    // },
    {
      title: "AI推荐型号",
      key: "aiRecommendModel",
      dataKey: "aiRecommendModel",
      hidden: true,
      width: 260
    },
    {
      title: "数量",
      key: "quantity",
      dataKey: "quantity",
      width: 110
    },
    {
      title: "单位",
      key: "unit",
      dataKey: "unit",
      align: "center",
      width: 90
    },
    {
      title: "短米折扣 (%)",
      key: "shortMeterDiscount",
      dataKey: "shortMeterDiscount",
      width: 120,
      hidden: false
    },
    {
      title: "折扣1 (%)",
      key: "discount1",
      dataKey: "discount1",
      width: 95,
      hidden: false
    },
    {
      title: "折扣2 (%)",
      key: "discount2",
      dataKey: "discount2",
      width: 95,
      hidden: false
    },
    {
      title: "折扣3 (%)",
      key: "discount3",
      dataKey: "discount3",
      width: 95,
      hidden: false
    },
    {
      title: "折扣4 (%)",
      key: "discount4",
      dataKey: "discount4",
      width: 95,
      hidden: false
    },
    {
      title: "折扣前单价",
      key: "discountBeforeUnitPrice",
      dataKey: "discountBeforeUnitPrice",
      width: 110,
      afterFreeze: true,
      align: "right",
      fixed: FixedDir.RIGHT
    },
    {
      title: "含税单价",
      key: "taxUnitPrice",
      dataKey: "taxUnitPrice",
      width: 95,
      align: "right",
      afterFreeze: true,
      fixed: FixedDir.RIGHT
    },
    {
      title: "含税合计",
      key: "taxTotalAmount",
      dataKey: "taxTotalAmount",
      align: "right",
      width: 95,
      afterFreeze: true,
      fixed: FixedDir.RIGHT
    },
    {
      title: "匹配说明",
      key: "priceMatchRecord",
      dataKey: "priceMatchRecord",
      width: 100,
      afterFreeze: true,
      fixed: FixedDir.RIGHT
    },
    {
      title: "",
      key: "matchStatus",
      dataKey: "matchStatus",
      fixed: FixedDir.RIGHT,
      afterFreeze: true,
      width: 30
    }
    // {
    //   title: "操作",
    //   key: "action",
    //   dataKey: "action",
    //   fixed: FixedDir.RIGHT,
    //   width: 70
    // }
  ];

  const originalColumnsWidth: { [key: string]: number } = {
    selection: 30,
    rowIndex: 60,
    excelRowIndex: 80,
    columnRawContent: 220,
    aiParseModel: 120,
    modelName: 250,
    voltageLevel: 90,
    specification: 120,
    priceMatchRecord: 90,
    aiRecommendModel: 220,
    unit: 90,
    quantity: 110,
    shortMeterDiscount: 105,
    discount1: 88,
    discount2: 88,
    discount3: 88,
    discount4: 88,
    taxUnitPrice: 88,
    discountBeforeUnitPrice: 100,
    taxTotalAmount: 100,
    matchStatus: 40,
    action: 80
  };

  function getColumnsWidthByRate(rate = 1) {
    const widths = Object.keys(originalColumnsWidth).reduce((acc, key) => {
      acc[key] = originalColumnsWidth[key] * rate;
      return acc;
    }, {} as { [key: string]: number });

    return widths;
  }

  return { columns, originalColumnsWidth, getColumnsWidthByRate };
}

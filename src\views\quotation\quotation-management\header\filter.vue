<template>
  <el-popover :visible="modelValue" placement="bottom" :width="300" @hide="onHidden()">
    <el-input v-model="state.keyword" placeholder="请输入" clearable @keydown.enter="onConfirmSearch()">
      <template #prepend>
        <el-button :icon="Search" />
      </template>
    </el-input>
    <div class="flex items-center justify-between mt-5">
      <el-button type="primary" @click="onClear()">清除</el-button>
      <div>
        <el-button @click="onCancel()">取消</el-button>
        <el-button type="primary" @click="onConfirmSearch()">确定</el-button>
      </div>
    </div>
    <template #reference>
      <div />
    </template>
  </el-popover>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { computed, reactive, watch } from "vue";

const emits = defineEmits<{
  (e: "onSearch", keyword: string): void;
  (e: "update:modelValue", value: boolean): void;
}>();

const props = withDefaults(defineProps<{ modelValue?: boolean; keyword?: string }>(), {
  modelValue: false,
  keyword: ""
});

const state = reactive<{
  keyword?: string;
  preKeyword?: string;
  visible?: boolean;
}>({
  visible: false
});

watch(
  () => props.keyword,
  keyword => {
    state.keyword = keyword;
    state.preKeyword = keyword;
  }
);

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emits("update:modelValue", val);
  }
});

// const showFilter = computed(() => state.visible || state.keyword?.trim());

// const onShowFilter = () => {
//   state.visible = true;
// };

const onHidden = () => {
  modelValue.value = false;
};

const onClear = () => {
  state.keyword = "";
  emits("onSearch", state.keyword);
  onClose();
};

const onCancel = () => {
  state.keyword = state.preKeyword;
  onClose();
};
const onConfirmSearch = () => {
  state.preKeyword = state.keyword;
  modelValue.value = false;
  emits("onSearch", state.keyword);
  onClose();
};

const onClose = () => {
  modelValue.value = false;
};
</script>

<style scoped lang="scss">
.show-filter {
  visibility: visible !important;

  &.show-filter-highlight {
    color: var(--el-color-primary);
  }
}
</style>

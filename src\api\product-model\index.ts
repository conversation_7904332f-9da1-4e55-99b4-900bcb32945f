import { http } from "@/utils/http";
import { withApiGateway } from "@/api/util";
import { IResponse, IProductCategoryReq, IProductModel } from "@/models";

/** 根据产品分类查询产品型号 */
export const queryAllProductModelByCategoryId = (categoryId: string) => {
  const url: string = withApiGateway(`admin-api/business/productModel/getListByCategoryId/${categoryId}`);
  return http.get<IProductCategoryReq, IResponse<Array<IProductModel>>>(url);
};

/** 根据分类id 获取全部分类下的型号 */
export const queryAllProductModel = (categoryId?: string) => {
  const url: string = withApiGateway(`admin-api/business/productModel/getListByCategoryId/${categoryId}`);
  return http.get<void, IResponse<Array<IProductModel>>>(url);
};
